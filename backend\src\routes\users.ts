import { Router } from 'express';
import { param, query, validationResult } from 'express-validator';
import { authenticateToken } from '@/middleware/auth';
import { getUserProfile, getUserActivity, searchUsers } from '@/controllers/userController';

const router = Router();

// =====================================================
// VALIDATION RULES
// =====================================================

const userIdValidation = [
  param('userId')
    .isUUID()
    .withMessage('User ID must be a valid UUID')
];

const activityQueryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('type')
    .optional()
    .isIn(['all', 'created', 'updated', 'completed', 'cancelled'])
    .withMessage('Type must be one of: all, created, updated, completed, cancelled')
];

const searchQueryValidation = [
  query('q')
    .isLength({ min: 2, max: 100 })
    .withMessage('Search query must be between 2 and 100 characters'),
  query('department')
    .optional()
    .isUUID()
    .withMessage('Department must be a valid UUID'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
];

// =====================================================
// VALIDATION MIDDLEWARE
// =====================================================

const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array().map((error: any) => ({
        field: error.path || error.param,
        message: error.msg,
        value: error.value
      }))
    });
  }
  next();
};

// =====================================================
// ROUTES
// =====================================================

/**
 * @route   GET /api/users/search
 * @desc    Search for users by name or email
 * @access  Private
 * @query   q - Search query (required, min 2 chars)
 * @query   department - Filter by department UUID (optional)
 * @query   limit - Number of results (optional, default 10, max 50)
 * @returns Array of matching users with basic info
 */
router.get('/search',
  authenticateToken,
  searchQueryValidation,
  handleValidationErrors,
  searchUsers
);

/**
 * @route   GET /api/users/:userId/profile
 * @desc    Get detailed user profile
 * @access  Private (same department or admin)
 * @param   userId - User UUID
 * @returns User profile with statistics and current task
 */
router.get('/:userId/profile',
  authenticateToken,
  userIdValidation,
  handleValidationErrors,
  getUserProfile
);

/**
 * @route   GET /api/users/:userId/activity
 * @desc    Get user activity/task history
 * @access  Private (same department or admin)
 * @param   userId - User UUID
 * @query   page - Page number (optional, default 1)
 * @query   limit - Items per page (optional, default 20, max 100)
 * @query   type - Activity type filter (optional, default 'all')
 * @returns Paginated user activity history
 */
router.get('/:userId/activity',
  authenticateToken,
  userIdValidation,
  activityQueryValidation,
  handleValidationErrors,
  getUserActivity
);

export default router;
