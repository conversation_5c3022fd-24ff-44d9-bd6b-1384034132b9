import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { cn } from '../../utils/cn';
import Button from './Button';
import Modal from './Modal';
import Badge from './Badge';
import StatusIndicator from './StatusIndicator';
import Clock from './Clock';
import StatusUpdateForm, { EnhancedTaskData } from '../StatusUpdateForm';
import TaskActionModal from '../TaskActionModal';
import TaskDetailsModal from '../TaskDetailsModal';
import { API_BASE_URL } from '../../config/api';
import { fetchCurrentTask, getTaskStatusText, isTaskExpired, getTimeRemaining } from '../../utils/taskUtils';

export interface FloatingHeaderProps {
  className?: string;
  onStatusUpdate?: () => void;
  onViewProfile?: (userId: string) => void;
}

interface ProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: any;
  onStatusUpdate?: () => void;
  onOpenTaskUpdate?: () => void;
  onViewProfile?: () => void;
  currentTask?: any;
  isLoadingTask?: boolean;
}

const ProfileModal: React.FC<ProfileModalProps> = ({ isOpen, onClose, user, onStatusUpdate, onOpenTaskUpdate, onViewProfile, currentTask, isLoadingTask }) => {
  const [currentStatus, setCurrentStatus] = useState<'active' | 'idle' | 'offline'>('active');
  const [isEditingStatus, setIsEditingStatus] = useState(false);

  const handleStatusSubmit = () => {
    if (onStatusUpdate) {
      onStatusUpdate();
    }
    setIsEditingStatus(false);
    onClose();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'idle': return 'warning';
      case 'offline': return 'neutral';
      default: return 'neutral';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return '🟢';
      case 'idle': return '🟡';
      case 'offline': return '⚫';
      default: return '⚫';
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Profile & Status" size="lg">
      <div className="space-y-6">
        {/* Profile Info */}
        <div className="flex items-center space-x-4 p-4 bg-neutral-50 rounded-lg">
          <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xl font-bold">
            {user?.name?.charAt(0)?.toUpperCase() || 'U'}
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-neutral-900">{user?.name || 'Unknown User'}</h3>
            <p className="text-sm text-neutral-600">{user?.email || 'No email'}</p>
            <div className="flex items-center mt-2">
              <StatusIndicator status={currentStatus} size="sm" />
              <Badge variant={getStatusColor(currentStatus)} size="sm" className="ml-2">
                {getStatusIcon(currentStatus)} {currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1)}
              </Badge>
            </div>
          </div>
        </div>

        {/* Status Update Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-md font-medium text-neutral-900">Current Status</h4>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => {
                if (onOpenTaskUpdate) {
                  onOpenTaskUpdate();
                }
              }}
            >
              Update Task
            </Button>
          </div>

          {isEditingStatus ? (
            <div className="space-y-4 p-4 bg-neutral-50 rounded-lg">
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  Current Task
                </label>
                <div className="p-3 bg-white border border-neutral-200 rounded-md min-h-[80px] text-sm text-neutral-600">
                  {isLoadingTask
                    ? 'Loading...'
                    : getTaskStatusText(!!currentTask, currentTask?.task_description, currentTask?.expected_finish_datetime)
                  }
                  {currentTask?.expected_finish_datetime && (
                    <div className={`mt-2 text-xs ${
                      isTaskExpired(currentTask.expected_finish_datetime)
                        ? 'text-orange-600'
                        : 'text-blue-600'
                    }`}>
                      {getTimeRemaining(currentTask.expected_finish_datetime)}
                    </div>
                  )}
                </div>
                <p className="text-xs text-neutral-500 mt-1">
                  Use "Update Task" button to modify your current task
                </p>
              </div>

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-neutral-700 mb-2">
                  Status
                </label>
                <select
                  id="status"
                  value={currentStatus}
                  onChange={(e) => setCurrentStatus(e.target.value as 'active' | 'idle' | 'offline')}
                  className="input"
                >
                  <option value="active">🟢 Active - Currently working</option>
                  <option value="idle">🟡 Idle - Available but not on task</option>
                  <option value="offline">⚫ Offline - Away from work</option>
                </select>
              </div>

              <div className="flex space-x-3">
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleStatusSubmit}
                  className="flex-1"
                >
                  Update Status
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setIsEditingStatus(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <div className="p-4 bg-neutral-50 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <p className="text-sm text-neutral-600">Current Task:</p>
                  <p className={`font-medium ${isTaskExpired(currentTask?.expected_finish_datetime) ? 'text-orange-600' : 'text-neutral-900'}`}>
                    {isLoadingTask
                      ? 'Loading...'
                      : getTaskStatusText(!!currentTask, currentTask?.task_description, currentTask?.expected_finish_datetime)
                    }
                  </p>
                  {currentTask?.expected_finish_datetime && (
                    <p className={`text-xs mt-1 ${
                      isTaskExpired(currentTask.expected_finish_datetime)
                        ? 'text-orange-600'
                        : 'text-blue-600'
                    }`}>
                      {getTimeRemaining(currentTask.expected_finish_datetime)}
                    </p>
                  )}
                </div>
                <div className="text-right">
                  <p className="text-sm text-neutral-600">Status:</p>
                  <div className="flex items-center justify-end mt-1">
                    <StatusIndicator status={currentStatus} size="sm" />
                    <span className="ml-2 text-sm font-medium text-neutral-900">
                      {currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Task Actions */}
              {currentTask && (
                <div className="flex space-x-2 pt-3 border-t border-neutral-200">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => handleViewTaskDetails()}
                    className="flex-1"
                  >
                    View Details
                  </Button>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => setIsEditingStatus(true)}
                    className="flex-1"
                  >
                    Update
                  </Button>
                  <Button
                    variant="error"
                    size="sm"
                    onClick={() => handleCancelTask()}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="space-y-3">
          <h4 className="text-md font-medium text-neutral-900">Quick Actions</h4>
          <div className="grid grid-cols-3 gap-3">
            <Button
              variant="primary"
              size="sm"
              onClick={() => {
                if (onOpenTaskUpdate) onOpenTaskUpdate();
              }}
              className="flex flex-col items-center p-3 h-auto"
            >
              <span className="text-lg mb-1">✏️</span>
              <span className="text-xs">Update Task</span>
            </Button>
            {onViewProfile && (
              <Button
                variant="secondary"
                size="sm"
                onClick={() => {
                  onClose();
                  if (onViewProfile) onViewProfile();
                }}
                className="flex flex-col items-center p-3 h-auto"
              >
                <span className="text-lg mb-1">👤</span>
                <span className="text-xs">Full Profile</span>
              </Button>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

const FloatingHeader: React.FC<FloatingHeaderProps> = ({ className, onStatusUpdate, onViewProfile }) => {
  const { user, logout } = useAuth();
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isTaskUpdateOpen, setIsTaskUpdateOpen] = useState(false);
  const [taskActionState, setTaskActionState] = useState<{
    isOpen: boolean;
    taskData?: EnhancedTaskData;
    taskId?: string;
  }>({ isOpen: false });

  const [currentTask, setCurrentTask] = useState<any>(null);
  const [isLoadingTask, setIsLoadingTask] = useState(false);
  const [isTaskDetailsOpen, setIsTaskDetailsOpen] = useState(false);

  // Fetch current task on component mount and when user changes
  useEffect(() => {
    const loadCurrentTask = async () => {
      if (!user) return;

      setIsLoadingTask(true);
      try {
        const taskResponse = await fetchCurrentTask();
        setCurrentTask(taskResponse?.data || null);
      } catch (error) {
        console.error('Error loading current task:', error);
      } finally {
        setIsLoadingTask(false);
      }
    };

    loadCurrentTask();
  }, [user]);

  const handleStatusUpdate = () => {
    if (onStatusUpdate) {
      onStatusUpdate();
    }
  };

  const handleOpenTaskUpdate = () => {
    setIsProfileOpen(false);
    setIsTaskUpdateOpen(true);
  };

  const handleTaskUpdateSubmit = async (taskData: EnhancedTaskData) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.error('No authentication token found');
        return;
      }

      // Transform the frontend data to match the backend API format
      const apiPayload: any = {
        task_description: taskData.task,
        status: taskData.status,
        priority: taskData.priority,
        category: taskData.taskCategory,
        estimated_duration_minutes: taskData.taskDurationMinutes,
        progress_percentage: taskData.progressPercentage,
        tags: taskData.tags.map(tag => tag.label)
      };

      // Add optional fields if they exist
      if (taskData.relatedProject) {
        apiPayload.project_id = taskData.relatedProject;
      }

      if (taskData.expectedFinishDateTime) {
        apiPayload.expected_completion_date = taskData.expectedFinishDateTime;
      }

      if (taskData.blockingIssues) {
        apiPayload.blocking_issues = taskData.blockingIssues;
      }

      if (taskData.numberOfQuestions) {
        apiPayload.number_of_questions = taskData.numberOfQuestions;
      }

      console.log('Submitting task update:', apiPayload);

      const response = await fetch(`${API_BASE_URL}/api/tasks/update`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiPayload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Task update failed:', errorText);

        // If unauthorized, clear the token and redirect to login
        if (response.status === 401) {
          console.error('Authentication failed - clearing stored token');
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          // Optionally reload the page to trigger auth redirect
          window.location.reload();
          return;
        }

        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('Task updated successfully:', result);

      // Show task action modal instead of immediately closing
      setTaskActionState({
        isOpen: true,
        taskData: taskData,
        taskId: result.data?.id || result.data?.task_id
      });

      setIsTaskUpdateOpen(false);
    } catch (error) {
      console.error('Error updating task:', error);
      // You might want to show an error message to the user here
    }
  };

  const handleTaskUpdateCancel = () => {
    setIsTaskUpdateOpen(false);
  };

  const handleTaskActionClose = async () => {
    setTaskActionState({ isOpen: false });

    // Refresh current task
    try {
      const taskResponse = await fetchCurrentTask();
      setCurrentTask(taskResponse?.data || null);
    } catch (error) {
      console.error('Error refreshing current task:', error);
    }

    if (onStatusUpdate) {
      onStatusUpdate();
    }
  };

  const handleTaskEdit = () => {
    // Close the action modal and reopen the status update form
    setTaskActionState({ isOpen: false });
    setIsTaskUpdateOpen(true);
  };

  const handleTaskKeep = () => {
    // Close modal and refresh
    handleTaskActionClose();
  };

  const handleViewOwnProfile = () => {
    if (user && onViewProfile) {
      onViewProfile(user.id);
    }
  };

  const handleViewTaskDetails = () => {
    if (!currentTask) return;
    setIsTaskDetailsOpen(true);
  };

  const handleCancelTask = async () => {
    if (!currentTask?.id) return;

    if (!confirm('Are you sure you want to cancel this task? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/api/task-updates/${currentTask.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to cancel task: ${response.status}`);
      }

      // Refresh current task
      const taskResponse = await fetchCurrentTask();
      setCurrentTask(taskResponse?.data || null);

      if (onStatusUpdate) {
        onStatusUpdate();
      }

      // Close the profile modal
      setIsProfileOpen(false);
    } catch (error) {
      console.error('Error canceling task:', error);
      alert('Failed to cancel task. Please try again.');
    }
  };

  return (
    <>
      <div className={cn(
        'floating-header animate-float',
        className
      )}>
        <div className="flex items-center space-x-3">
          {/* Clock */}
          <Clock
            size="sm"
            variant="badge"
            className="text-neutral-700"
            showSeconds={false}
          />

          {/* Divider */}
          <div className="w-px h-6 bg-neutral-300" />

          {/* Profile Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsProfileOpen(true)}
            className="rounded-full p-2 hover:bg-neutral-100"
            title={`Profile: ${user?.name || 'User'}`}
            aria-label={`Open profile for ${user?.name || 'User'}`}
            icon={
              <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                {user?.name?.charAt(0)?.toUpperCase() || 'U'}
              </div>
            }
          />

          {/* Divider */}
          <div className="w-px h-6 bg-neutral-300" />

          {/* Quick Status Update Button */}
          <div className="flex items-center">
            <Button
              variant="primary"
              size="sm"
              onClick={() => handleOpenTaskUpdate()}
              className="rounded-full px-3 py-2"
              title="Update My Task"
            >
              ✏️ Update Task
            </Button>
          </div>

          {/* Divider */}
          <div className="w-px h-6 bg-neutral-300" />

          {/* Logout Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={logout}
            className="rounded-full p-2 hover:bg-red-50 hover:text-red-600"
            title="Sign Out"
            aria-label="Sign out of your account"
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            }
          />
        </div>
      </div>

      {/* Profile Modal */}
      <ProfileModal
        isOpen={isProfileOpen}
        onClose={() => setIsProfileOpen(false)}
        user={user}
        onStatusUpdate={handleStatusUpdate}
        onOpenTaskUpdate={handleOpenTaskUpdate}
        onViewProfile={handleViewOwnProfile}
        currentTask={currentTask}
        isLoadingTask={isLoadingTask}
      />

      {/* Task Update Form */}
      {isTaskUpdateOpen && user && (
        <StatusUpdateForm
          employeeId={user.id}
          currentTask=""
          onSubmit={handleTaskUpdateSubmit}
          onCancel={handleTaskUpdateCancel}
        />
      )}

      {/* Task Action Modal */}
      {taskActionState.isOpen && taskActionState.taskData && (
        <TaskActionModal
          isOpen={taskActionState.isOpen}
          onClose={handleTaskActionClose}
          taskData={taskActionState.taskData}
          taskId={taskActionState.taskId}
          onEdit={handleTaskEdit}
          onKeep={handleTaskKeep}
        />
      )}

      {/* Task Details Modal */}
      {isTaskDetailsOpen && currentTask && (
        <TaskDetailsModal
          isOpen={isTaskDetailsOpen}
          onClose={() => setIsTaskDetailsOpen(false)}
          taskData={currentTask}
        />
      )}
    </>
  );
};

export default FloatingHeader;
