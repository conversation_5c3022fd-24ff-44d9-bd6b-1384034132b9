import React, { useState } from 'react';
import { <PERSON><PERSON>, Button, Badge } from './ui';
import { EnhancedTaskData } from './StatusUpdateForm';
import { API_BASE_URL } from '../config/api';

interface TaskActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  taskData: EnhancedTaskData;
  taskId?: string;
  onEdit: () => void;
  onKeep: () => void;
}

const TaskActionModal: React.FC<TaskActionModalProps> = ({
  isOpen,
  onClose,
  taskData,
  taskId,
  onEdit,
  onKeep
}) => {
  const [isDiscarding, setIsDiscarding] = useState(false);
  const [showDiscardConfirm, setShowDiscardConfirm] = useState(false);

  const handleDiscard = async () => {
    if (!taskId) {
      console.error('No task ID provided for discard');
      return;
    }

    setIsDiscarding(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.error('No authentication token found');
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/tasks/${taskId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Failed to discard task:', errorData);
        return;
      }

      const result = await response.json();
      console.log('Task discarded successfully:', result);
      
      // Close modal and notify parent
      onClose();
    } catch (error) {
      console.error('Error discarding task:', error);
    } finally {
      setIsDiscarding(false);
      setShowDiscardConfirm(false);
    }
  };

  const formatDuration = (minutes: number): string => {
    if (minutes < 60) return `${minutes} minutes`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
    return `${hours}h ${remainingMinutes}m`;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'primary';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'idle': return 'warning';
      case 'offline': return 'neutral';
      default: return 'neutral';
    }
  };

  if (showDiscardConfirm) {
    return (
      <Modal isOpen={isOpen} onClose={() => setShowDiscardConfirm(false)} title="Confirm Discard" size="md">
        <div className="space-y-4">
          <div className="flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200">
            <div className="flex-shrink-0">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-medium text-red-900">Are you sure?</h3>
              <p className="text-sm text-red-700 mt-1">
                This will permanently delete your task. This action cannot be undone.
              </p>
            </div>
          </div>

          <div className="p-3 bg-neutral-50 rounded-lg">
            <p className="text-sm font-medium text-neutral-700 mb-1">Task to be deleted:</p>
            <p className="text-sm text-neutral-900">{taskData.task}</p>
          </div>

          <div className="flex space-x-3 pt-4">
            <Button
              variant="error"
              onClick={handleDiscard}
              disabled={isDiscarding}
              className="flex-1"
            >
              {isDiscarding ? 'Discarding...' : 'Yes, Discard Task'}
            </Button>
            <Button
              variant="secondary"
              onClick={() => setShowDiscardConfirm(false)}
              disabled={isDiscarding}
              className="flex-1"
            >
              Cancel
            </Button>
          </div>
        </div>
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Task Created Successfully!" size="2xl">
      <div className="space-y-6">
        {/* Success Message */}
        <div className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg border border-green-200">
          <div className="flex-shrink-0">
            <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-medium text-green-900">Your task has been created!</h3>
            <p className="text-sm text-green-700 mt-1">
              You can now edit, discard, or keep this task as is.
            </p>
          </div>
        </div>

        {/* Task Summary */}
        <div className="bg-neutral-50 rounded-lg p-4 space-y-4">
          <h4 className="font-medium text-neutral-900">Task Summary</h4>
          
          <div className="space-y-3">
            <div>
              <p className="text-sm font-medium text-neutral-700 mb-1">Description:</p>
              <p className="text-sm text-neutral-900 bg-white p-3 rounded border">{taskData.task}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-neutral-700 mb-1">Status:</p>
                <Badge variant={getStatusColor(taskData.status)} size="sm">
                  {taskData.status.charAt(0).toUpperCase() + taskData.status.slice(1)}
                </Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-neutral-700 mb-1">Priority:</p>
                <Badge variant={getPriorityColor(taskData.priority)} size="sm">
                  {taskData.priority.charAt(0).toUpperCase() + taskData.priority.slice(1)}
                </Badge>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-neutral-700 mb-1">Category:</p>
                <p className="text-sm text-neutral-900">{taskData.taskCategory.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-neutral-700 mb-1">Duration:</p>
                <p className="text-sm text-neutral-900">{formatDuration(taskData.taskDurationMinutes)}</p>
              </div>
            </div>

            {taskData.tags.length > 0 && (
              <div>
                <p className="text-sm font-medium text-neutral-700 mb-2">Tags:</p>
                <div className="flex flex-wrap gap-2">
                  {taskData.tags.map((tag) => (
                    <span
                      key={tag.id}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                      style={{ backgroundColor: tag.color + '20', color: tag.color }}
                    >
                      {tag.label}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3 pt-4 border-t border-neutral-200">
          <Button
            variant="primary"
            onClick={onKeep}
            className="flex-1"
          >
            Keep Task
          </Button>
          <Button
            variant="secondary"
            onClick={onEdit}
            className="flex-1"
          >
            Edit Task
          </Button>
          <Button
            variant="error"
            onClick={() => setShowDiscardConfirm(true)}
            className="flex-1"
          >
            Discard Task
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default TaskActionModal;
