import React from 'react';
import { Modal, Badge } from './ui';

interface TaskDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  taskData: {
    id?: string;
    task_description?: string;
    status?: string;
    priority?: string;
    category?: string;
    estimated_duration_minutes?: number;
    progress_percentage?: number;
    project_name?: string;
    blocking_issues?: string;
    number_of_questions?: number;
    expected_finish_datetime?: string;
    created_at?: string;
    updated_at?: string;
  };
}

const TaskDetailsModal: React.FC<TaskDetailsModalProps> = ({
  isOpen,
  onClose,
  taskData
}) => {
  const formatDuration = (minutes: number) => {
    if (!minutes) return 'Not specified';
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (hours === 0) return `${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
    if (remainingMinutes === 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
    return `${hours}h ${remainingMinutes}m`;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'primary';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'active': return 'primary';
      case 'paused': return 'warning';
      case 'cancelled': return 'error';
      default: return 'neutral';
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Task Details" size="2xl">
      <div className="space-y-6">
        {/* Task Description */}
        <div>
          <p className="text-sm font-medium text-neutral-700 mb-2">Task Description:</p>
          <div className="bg-neutral-50 p-4 rounded-lg border">
            <p className="text-sm text-neutral-900 leading-relaxed">
              {taskData.task_description || 'No description available'}
            </p>
          </div>
        </div>

        {/* Status and Priority */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-neutral-700 mb-2">Status:</p>
            <Badge variant={getStatusColor(taskData.status || '')} size="sm">
              {taskData.status ? taskData.status.charAt(0).toUpperCase() + taskData.status.slice(1) : 'Unknown'}
            </Badge>
          </div>
          <div>
            <p className="text-sm font-medium text-neutral-700 mb-2">Priority:</p>
            <Badge variant={getPriorityColor(taskData.priority || '')} size="sm">
              {taskData.priority ? taskData.priority.charAt(0).toUpperCase() + taskData.priority.slice(1) : 'Unknown'}
            </Badge>
          </div>
        </div>

        {/* Category and Duration */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-neutral-700 mb-2">Category:</p>
            <p className="text-sm text-neutral-900">
              {taskData.category ? taskData.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'General'}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-neutral-700 mb-2">Estimated Duration:</p>
            <p className="text-sm text-neutral-900">{formatDuration(taskData.estimated_duration_minutes || 0)}</p>
          </div>
        </div>

        {/* Progress */}
        <div>
          <p className="text-sm font-medium text-neutral-700 mb-2">Progress:</p>
          <div className="flex items-center space-x-3">
            <div className="flex-1 bg-neutral-200 rounded-full h-3">
              <div 
                className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                style={{ width: `${taskData.progress_percentage || 0}%` }}
              ></div>
            </div>
            <span className="text-sm font-medium text-neutral-900 min-w-[3rem]">
              {taskData.progress_percentage || 0}%
            </span>
          </div>
        </div>

        {/* Questions Count */}
        <div>
          <p className="text-sm font-medium text-neutral-700 mb-2">Number of Questions:</p>
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm text-neutral-900">
              {taskData.number_of_questions || 0} question{(taskData.number_of_questions || 0) !== 1 ? 's' : ''}
            </span>
          </div>
        </div>

        {/* Project Information */}
        {taskData.project_name && (
          <div>
            <p className="text-sm font-medium text-neutral-700 mb-2">Related Project:</p>
            <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
              <p className="text-sm text-blue-900 font-medium">{taskData.project_name}</p>
            </div>
          </div>
        )}

        {/* Expected Completion */}
        {taskData.expected_finish_datetime && (
          <div>
            <p className="text-sm font-medium text-neutral-700 mb-2">Expected Completion:</p>
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm text-neutral-900">
                {new Date(taskData.expected_finish_datetime).toLocaleString()}
              </span>
            </div>
          </div>
        )}

        {/* Blocking Issues */}
        {taskData.blocking_issues && (
          <div>
            <p className="text-sm font-medium text-neutral-700 mb-2">Blocking Issues:</p>
            <div className="bg-red-50 p-3 rounded-lg border border-red-200">
              <div className="flex items-start space-x-2">
                <svg className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <p className="text-sm text-red-800">{taskData.blocking_issues}</p>
              </div>
            </div>
          </div>
        )}

        {/* Timestamps */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-neutral-200">
          {taskData.created_at && (
            <div>
              <p className="text-xs font-medium text-neutral-500 mb-1">Created:</p>
              <p className="text-xs text-neutral-700">
                {new Date(taskData.created_at).toLocaleString()}
              </p>
            </div>
          )}
          {taskData.updated_at && (
            <div>
              <p className="text-xs font-medium text-neutral-500 mb-1">Last Updated:</p>
              <p className="text-xs text-neutral-700">
                {new Date(taskData.updated_at).toLocaleString()}
              </p>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default TaskDetailsModal;
